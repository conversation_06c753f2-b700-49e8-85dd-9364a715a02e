document.addEventListener('DOMContentLoaded', () => {
    const searchInput = document.getElementById('searchEmployee');
    const placeholderText = "Vyhledat zaměstnance";
    let placeholderIndex = 0;

    function typePlaceholder() {
        if (placeholderIndex < placeholderText.length) {
            searchInput.placeholder += placeholderText.charAt(placeholderIndex);
            placeholderIndex++;
            setTimeout(typePlaceholder, 100);
        }
    }

    typePlaceholder();

    searchInput.addEventListener('focus', () => {
        searchInput.placeholder = '';
        searchInput.style.backgroundColor = '#fff';
    });

    searchInput.addEventListener('blur', () => {
        if (searchInput.value === '') {
            placeholderIndex = 0;
            searchInput.placeholder = '';
            typePlaceholder();
        }
        searchInput.style.backgroundColor = '';
    });


    const employeeImages = document.querySelectorAll('.employee img');

    function addSpinEffect() {
        employeeImages.forEach(img => {
            img.addEventListener('mouseenter', function() {
                if (!this.classList.contains('spun')) {
                    this.classList.add('spin');
                    this.addEventListener('animationend', function() {
                        this.classList.remove('spin');
                        this.classList.add('spun');
                    }, { once: true });
                }
            });
        });
    }


    addSpinEffect();
});

const employee = document.querySelectorAll('.employee');

const removeDiacritics = (text) => {
    return text.normalize("NFD").replace(/[\u0300-\u036f]/g, "");
};

function sortEmployeesAlphabetically() {
    const employeeList = document.querySelector('.employee_listing');
    const employees = Array.from(employeeList.children);

    employees.sort((a, b) => {
        const nameA = removeDiacritics(a.querySelector('.employee_name').textContent.trim().toUpperCase());
        const nameB = removeDiacritics(b.querySelector('.employee_name').textContent.trim().toUpperCase());
        return nameA.localeCompare(nameB, 'cs');
    });

    employeeList.innerHTML = '';
    employees.forEach(employee => employeeList.appendChild(employee));

    updateEmployeeNumbers();
}

const displayEmployees = (values) => {
    const normalizedValues = removeDiacritics(values.toUpperCase());
    const activeFilter = document.querySelector('.filter-btn.active').getAttribute('data-filter');

    employee.forEach(element => {
        element.style.display = "none";

        const name = removeDiacritics(element.children[1].innerHTML.toUpperCase());
        const position = removeDiacritics(element.children[2].innerHTML.toUpperCase());
        const job_type = removeDiacritics(element.children[3].innerHTML.toUpperCase());

        const matchesSearch = name.includes(normalizedValues) || position.includes(normalizedValues) || job_type.includes(normalizedValues);
        const matchesFilter = activeFilter === 'all' || element.dataset.departments.includes(activeFilter);

        if (matchesSearch && matchesFilter) {
            element.style.display = "block";
        }
    });

    sortEmployeesAlphabetically();
};

const filterButtons = document.querySelectorAll('.filter-btn');
const allEmployees = document.querySelectorAll('.employee');

filterButtons.forEach(button => {
    button.addEventListener('click', () => {
        const filter = button.getAttribute('data-filter');
        
        filterButtons.forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');

        allEmployees.forEach(employee => {
            if (filter === 'all' || employee.dataset.departments.includes(filter)) {
                employee.style.display = 'block';
            } else {
                employee.style.display = 'none';
            }
        });

        sortEmployeesAlphabetically();
        updateEmployeeNumbers();
    });
});

const searchInput = document.getElementById('searchEmployee');
const searchBtn = document.getElementById('searchBtn');
const searchForm = document.querySelector('.search_form');
const employees = document.querySelectorAll('.employee');

function searchEmployees() {
    const searchTerm = removeDiacritics(searchInput.value.toLowerCase());
    employees.forEach(employee => {
        const name = removeDiacritics(employee.querySelector('.employee_name').textContent.toLowerCase());
        const position = removeDiacritics(employee.querySelector('.position').textContent.toLowerCase()); // Ensure position is included
        const department = removeDiacritics(employee.querySelector('.job_type').textContent.toLowerCase());
        
        
        if (name.includes(searchTerm) || position.includes(searchTerm) || department.includes(searchTerm)) {
            employee.style.display = 'block';
        } else {
            employee.style.display = 'none';
        }
    });
    updateEmployeeNumbers();
}

searchInput.addEventListener('input', searchEmployees);

searchForm.addEventListener('submit', (e) => {
    e.preventDefault();
    searchEmployees();
});

searchBtn.addEventListener('click', (e) => {
    e.preventDefault();
    searchEmployees();
});

document.addEventListener('DOMContentLoaded', () => {
    sortEmployeesAlphabetically();
});

function updateEmployeeNumbers() {
    const employees = document.querySelectorAll('.employee');
    employees.forEach((employee, index) => {
        employee.setAttribute('data-number', index + 1);
    });
}

const modal = document.getElementById('employeeModal');
const modalImage = document.getElementById('modalImage');
const modalName = document.getElementById('modalName');
const modalPosition = document.getElementById('modalPosition');
const modalDepartment = document.getElementById('modalDepartment');
const modalOffice = document.getElementById('modalOffice');
const modalDescription = document.getElementById('modalDescription');
const closeBtn = document.getElementsByClassName('close')[0];

employee.forEach(emp => {
    emp.addEventListener('click', () => {
        const img = emp.querySelector('img');
        const name = emp.querySelector('.employee_name').textContent;
        const position = emp.querySelector('.position').textContent;
        const department = emp.querySelector('.job_type').textContent;
        
        const phone = emp.dataset.phone || 'undefined';
        const mobile = emp.dataset.mobile || 'undefined';
        const email = emp.dataset.email || 'undefined';
        const teamsLink = emp.dataset.teams || '#';

        modalImage.src = img.src;
        modalName.textContent = name;
        
        // Combine position, department, and office into one element
        modalPosition.textContent = `Pracovní pozice:\n${position}`;
        modalDepartment.textContent = `Oddělení:\n${department}`;
        modalOffice.textContent = `Kancelář: undefined`; // Update this if you have office data

        modalDescription.textContent = `Popis pracovní role: undefined`;
        document.getElementById('modalPhone').textContent = `Telefon: ${phone}`;
        document.getElementById('modalMobile').textContent = `Mobil: ${mobile}`;
        document.getElementById('modalEmail').textContent = `Email: ${email}`;

        const modalTeams = document.getElementById('modalTeams');
        modalTeams.innerHTML = `<img src="img/teams.png" alt="Teams Icon" class="icon teams-icon"> <a href="${teamsLink}" target="_blank">Chat on Teams</a>`;
        
        modal.style.display = 'block';
    });
});

function closeModal() {
    modal.classList.add('closing');
    setTimeout(() => {
        modal.style.display = 'none';
        modal.classList.remove('closing');
    }, 300);
}

closeBtn.onclick = closeModal;

window.onclick = (event) => {
    if (event.target == modal) {
        closeModal();
    }
};

// přepínač 
const toggleSwitch = document.querySelector('.theme-switch input[type="checkbox"]');
const currentTheme = localStorage.getItem('theme');

if (currentTheme) {
    document.documentElement.setAttribute('data-theme', currentTheme);
  
    if (currentTheme === 'dark') {
        toggleSwitch.checked = true;
        document.body.classList.add('dark-mode');
    }
}

function switchTheme(e) {
    if (e.target.checked) {
        document.documentElement.setAttribute('data-theme', 'dark');
        document.body.classList.add('dark-mode');
        localStorage.setItem('theme', 'dark');
    }
    else {
        document.documentElement.setAttribute('data-theme', 'light');
        document.body.classList.remove('dark-mode');
        localStorage.setItem('theme', 'light');
    }    
}

toggleSwitch.addEventListener('change', switchTheme, false);
