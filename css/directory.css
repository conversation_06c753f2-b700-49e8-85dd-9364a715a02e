.employee_directory {
    width: 90%;
    margin: 0 auto;
    margin-top: 150px;
    margin-bottom: 150px;
    font-family: 'N<PERSON><PERSON>', sans-serif;
    display: flex;
    background: #f8f9fa;
    border-radius: 16px;
    box-shadow: 0 4px 24px rgba(0,0,0,0.07);
    padding: 24px 0 24px 24px;
    gap: 32px;
}

.employee_directory h1 {
    text-align: center;
    font-size: 2em;
    color: #00808f;
    margin-bottom: 40px;
}

form.search_form {
    position: relative;
    margin-bottom: 20px;
}

form.search_form input {
    width: 100%;
    padding: 10px 10px;
    font-size: 1em;
    border: solid 1px #C6C6C6;
    border-radius: 5px;
    margin: 0 auto;
    transition: .2s ease-in;
}

form.search_form input:focus {
    border-color: #00808f;
    outline: none;
}

form.search_form i {
    position: absolute;
    top: 15px;
    right: 10px;
    color: #C6C6C6;
}

.employee_listing {
    background: #fff;
    border-radius: 16px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    padding: 24px 0;
    width: 340px;
    min-width: 320px;
    max-width: 400px;
    display: flex;
    flex-direction: column;
    gap: 18px;
    overflow-y: auto;
    height: 80vh;
}

.employee {
    display: flex;
    align-items: center;
    gap: 18px;
    padding: 12px 18px;
    border-radius: 12px;
    transition: background 0.2s, box-shadow 0.2s;
    cursor: pointer;
    background: #f4f7fb;
}

.employee:hover {
    background: #e0e7ef;
    box-shadow: 0 2px 8px rgba(37,99,235,0.08);
}

.employee img {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    object-fit: cover;
    box-shadow: 0 1px 4px rgba(0,0,0,0.08);
    border: 2px solid #e0e7ef;
}

.employee_name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #222;
    margin: 0;
}

.position, .job_type {
    font-size: 0.95rem;
    color: #2563eb;
    margin: 0;
}

.click-info {
    display: none;
}

/* Search and filter styles */
.search_form input[type="text"] {
    width: 100%;
    padding: 10px 16px;
    border-radius: 8px;
    border: 1px solid #e0e7ef;
    background: #f8f9fa;
    font-size: 1rem;
    margin-bottom: 18px;
    outline: none;
    transition: border 0.2s;
}

.search_form input[type="text"]:focus {
    border: 1.5px solid #2563eb;
}

.department-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 18px;
}

.filter-btn {
    background: #e0e7ef;
    color: #2563eb;
    border: none;
    border-radius: 8px;
    padding: 7px 16px;
    font-size: 0.98rem;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
}

.filter-btn.active, .filter-btn:hover {
    background: #2563eb;
    color: #fff;
}

.filter-btn i {
    margin-right: 5px;
}

@media (max-width: 768px) {
    .department-filters {
        flex-direction: column;
    }
    
    .filter-btn {
        width: 100%;
        margin: 5px 0;
    }

    .employee_listing .employee {
        width: 100%;
    }
}

.filter-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
}

.filter-header i {
    font-size: 24px;
    color: #00808f;
    margin-right: 10px;
}

.filter-header h2 {
    font-size: 20px;
    color: #00808f;
    margin: 0;
}

.department-filters {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 10px;
}

@media (min-width: 1200px) {
    .employee_listing {
        display: flex; 
        flex-wrap: wrap;
        justify-content: space-between;
        max-width: 1900px;
        margin-left: auto;
        margin-right: auto;
    }

    .employee_listing .employee {
        width: 350px;
        height: 420px;
        cursor: pointer;
    }

    form.search_form {
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 40px;
    }
}

@media (min-width: 1366px) {
    .employee_listing .employee {
        width: 300px;
    }
}

@media (max-width: 768px) {
    .employee_listing .employee {
        padding: 20px;
    }

    .employee_listing .employee img {
        max-width: 120px;
        max-height: 120px;
    }
}