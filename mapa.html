<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;700;900&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    body {
      font-family: 'Nunito', Arial, sans-serif;
      background: #f7f9fb;
      margin: 0;
      padding: 0;
      color: #222;
    }
    h1 {
      text-align: center;
      margin-top: 2.5rem;
      font-size: 2.6rem;
      font-weight: 900;
      color: #2563eb;
      letter-spacing: 0.01em;
      margin-bottom: 2.2rem;
    }
    .main-flex {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      justify-content: flex-start;
      width: 100vw;
      max-width: none;
      margin: 2rem 0 2rem 0;
      gap: 48px;
    }
    .side-panel {
      background: #fff;
      box-shadow: 0 8px 32px 0 rgba(37,99,235,0.10), 0 2px 8px 0 rgba(0,123,255,0.08);
      border-radius: 24px;
      border: 2.5px solid #e3e8f0;
      padding: 2.8rem 2rem 2.8rem 2.4rem;
      min-width: 320px;
      max-width: 420px;
      transition: box-shadow 0.25s, background 0.25s;
      position: sticky;
      top: 32px;
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      height: 900px;
      overflow-y: auto;
    }
    .side-panel h2 {
      font-size: 1.45rem;
      font-weight: 900;
      letter-spacing: 0.01em;
      margin-bottom: 1.3rem;
      background: none;
      position: sticky;
      top: 0;
      z-index: 2;
      padding-bottom: 0.7rem;
      background: #fff;
      color: #2563eb;
    }
    .search-box {
      margin-bottom: 1.5rem;
      border-radius: 12px;
      border: 2px solid #e0e7ef;
      padding: 16px 22px;
      font-size: 1.15rem;
      background: #f7f9fb;
      transition: border 0.2s;
      position: sticky;
      top: 54px;
      z-index: 3;
      outline: none;
      color: #222;
    }
    .search-box:focus {
      border: 2px solid #2563eb;
      background: #fff;
    }
    .employee-list {
      gap: 1.2rem;
      padding-top: 0.5rem;
      scrollbar-width: thin;
      scrollbar-color: #b3c0d1 #f7f7fa;
      flex: 1 1 auto;
      overflow-y: auto;
      max-height: 700px;
    }
    .employee-list li {
      display: flex;
      align-items: center;
      background: #f8faff;
      border-radius: 18px;
      box-shadow: 0 2px 12px rgba(37,99,235,0.07);
      padding: 16px 22px 16px 16px;
      margin-bottom: 0;
      font-size: 1.15rem;
      font-weight: 700;
      cursor: pointer;
      border: 2px solid transparent;
      transition: box-shadow 0.18s, background 0.18s, transform 0.18s, border 0.18s;
      min-height: 60px;
      position: relative;
      color: #222;
    }
    .employee-list li:hover, .employee-list li.active {
      background: linear-gradient(90deg, #e3f0ff 0%, #f7faff 100%);
      box-shadow: 0 4px 18px rgba(37,99,235,0.13);
      border: 2px solid #2563eb33;
      transform: translateY(-2px) scale(1.03);
      color: #2563eb;
    }
    .employee-list li:focus {
      outline: 2px solid #2563eb;
    }
    .avatar-img {
      width: 52px;
      height: 52px;
      border-radius: 50%;
      object-fit: cover;
      margin-right: 20px;
      border: 2.5px solid #e3e8f0;
      background: #fff;
      box-shadow: 0 2px 8px rgba(0,0,0,0.09);
      vertical-align: middle;
      flex-shrink: 0;
    }
    .employee-list .emp-name {
      font-weight: 900;
      font-size: 1.15rem;
      color: #222;
      margin-right: auto;
      letter-spacing: 0.01em;
      display: block;
      line-height: 1.2;
    }
    .find-btn {
      background: #2563eb;
      color: #fff;
      border: none;
      border-radius: 24px;
      padding: 10px 24px;
      font-size: 1.13rem;
      margin-left: 28px;
      cursor: pointer;
      transition: background 0.18s, color 0.18s, box-shadow 0.18s, transform 0.18s;
      font-weight: 800;
      display: flex;
      align-items: center;
      gap: 10px;
      box-shadow: 0 2px 8px rgba(37,99,235,0.10);
      min-height: 44px;
      min-width: 80px;
      justify-content: center;
    }
    .find-btn .fa-search {
      font-size: 1.25em;
      margin-right: 4px;
      color: #fff;
      filter: drop-shadow(0 1px 2px #1e40af33);
    }
    .find-btn:hover, .find-btn:focus {
      background: #1742b5;
      color: #fff;
      box-shadow: 0 4px 16px rgba(37,99,235,0.18);
      transform: translateY(-2px) scale(1.04);
    }
    .mapa-wrapper {
      flex: 1 1 auto;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      background: transparent;
      min-width: 0;
      margin-left: 0;
    }
    .office-map-container {
      position: relative;
      display: inline-block;
      background: #fff;
      box-shadow: 0 8px 32px rgba(37,99,235,0.10);
      border-radius: 24px;
      border: 2.5px solid #e3e8f0;
      padding: 0;
      margin: 0;
      max-width: 1920px;
      width: auto;
    }
    .office-map-container img {
      display: block;
      width: auto;
      max-width: 1920px;
      height: auto;
      -webkit-user-select: none;
      user-select: none;
      pointer-events: auto;
      background: transparent;
      border-radius: 24px;
      border: none;
    }
    .marker {
      width: 36px;
      height: 36px;
      background: transparent;
      border: none;
      border-radius: 50%;
      position: absolute;
      cursor: grab;
      transition: box-shadow 0.2s, background 0.2s, transform 0.2s;
      box-shadow: none;
      z-index: 2;
      display: flex;
      align-items: center;
      justify-content: center;
      outline: none;
      user-select: none;
      -webkit-user-select: none;
      padding: 0;
    }
    .marker:active {
      cursor: grabbing;
    }
    .marker.active {
      box-shadow: none;
      background: transparent;
      border: none;
    }
    .marker.added {
      animation: marker-pop 0.5s;
    }
    @keyframes marker-pop {
      0% { transform: scale(0.5); opacity: 0.2; }
      60% { transform: scale(1.3); opacity: 1; }
      100% { transform: scale(1); opacity: 1; }
    }
    .marker .tooltip {
      min-width: 120px;
      text-align: center;
    }
    .marker:hover .tooltip, .marker.active .tooltip {
      opacity: 1;
      pointer-events: auto;
    }
    .tooltip {
      position: absolute;
      top: -44px;
      left: 50%;
      transform: translateX(-50%);
      background: #222;
      color: #fff;
      padding: 7px 16px;
      border-radius: 8px;
      font-size: 16px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s;
      z-index: 10;
      box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    .error-message {
      color: #c62828;
      background: #fff3f3;
      border: 1px solid #ffcdd2;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      margin: 2rem auto;
      max-width: 600px;
      font-size: 1.2rem;
      display: none;
    }
    .mapping-hint {
      text-align: center;
      margin-bottom: 1rem;
      color: #444;
      font-size: 1.1rem;
      background: #fffbe7;
      border-radius: 8px;
      padding: 10px 20px;
      max-width: 900px;
      margin-left: auto;
      margin-right: auto;
      box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    }
    .mapping-dialog {
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border-radius: 10px;
      box-shadow: 0 4px 24px rgba(0,0,0,0.18);
      padding: 24px 32px;
      z-index: 10000;
      min-width: 320px;
      max-width: 90vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 1rem;
    }
    .mapping-dialog label {
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
    .mapping-dialog select {
      font-size: 1.1rem;
      padding: 6px 12px;
      border-radius: 6px;
      border: 1px solid #ccc;
      width: 100%;
    }
    .mapping-dialog button {
      margin-top: 1rem;
      padding: 8px 18px;
      border-radius: 6px;
      border: none;
      background: #007bff;
      color: #fff;
      font-size: 1.1rem;
      cursor: pointer;
      transition: background 0.2s;
    }
    .mapping-dialog button:hover {
      background: #0056b3;
    }
    @media (max-width: 1200px) {
      .main-flex {
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
      }
      .side-panel {
        border-radius: 18px 18px 0 0;
        height: 300px;
        max-width: 100vw;
        min-width: 0;
        width: 100vw;
        margin-bottom: 0;
        padding: 2rem 1.2rem 2rem 1.2rem;
      }
      .mapa-wrapper {
        justify-content: center;
      }
      .office-map-container {
        border-radius: 0 0 18px 18px;
      }
      .office-map-container img {
        border-radius: 0 0 18px 18px;
      }
    }
    .side-panel.collapsed {
      width: 38px !important;
      min-width: 38px !important;
      max-width: 38px !important;
      padding: 0 !important;
      overflow: hidden !important;
      transition: width 0.3s, min-width 0.3s, max-width 0.3s, padding 0.3s;
    }
    .side-panel.collapsed h2,
    .side-panel.collapsed .search-box,
    .side-panel.collapsed .employee-list,
    .side-panel.collapsed .find-btn,
    .side-panel.collapsed span {
      display: none !important;
    }
    .side-panel.collapsed #collapse-btn {
      left: 4px !important;
      right: auto !important;
      top: 8px !important;
    }
    .marker-avatar {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: none;
      box-shadow: none;
      background: none;
      display: block;
    }
    .marker:hover, .marker.active {
      box-shadow: none;
      background: transparent;
      border: none;
    }
    .employee-bar {
      width: 100%;
      margin: 0 0 1.5rem 0;
      background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
      box-shadow:
        0 8px 24px rgba(37,99,235,0.06),
        0 4px 8px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
      border-radius: 20px;
      border: 1px solid rgba(37,99,235,0.1);
      padding: 1.25rem 1.5rem;
      position: relative;
      z-index: 10;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      height: auto;
      min-height: 120px;
    }

    .search-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
      padding: 0.75rem 1rem;
      background: rgba(255,255,255,0.7);
      border-radius: 12px;
      border: 1px solid rgba(37,99,235,0.08);
      -webkit-backdrop-filter: blur(20px);
      backdrop-filter: blur(20px);
      height: 60px;
    }

    .search-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;
    }

    .employee-list-horizontal {
      display: flex;
      flex-direction: row;
      gap: 1rem;
      list-style: none;
      margin: 0;
      padding: 0.5rem 0;
      overflow-x: auto;
      overflow-y: hidden;
      scrollbar-width: thin;
      scrollbar-color: rgba(37,99,235,0.3) transparent;
      scroll-behavior: smooth;
      -webkit-overflow-scrolling: touch;
    }

    /* Webkit scrollbar styling */
    .employee-list-horizontal::-webkit-scrollbar {
      height: 8px;
    }

    .employee-list-horizontal::-webkit-scrollbar-track {
      background: #f7f7fa;
      border-radius: 4px;
    }

    .employee-list-horizontal::-webkit-scrollbar-thumb {
      background: #b3c0d1;
      border-radius: 4px;
    }

    .employee-list-horizontal::-webkit-scrollbar-thumb:hover {
      background: #9ca3af;
    }
    .employee-list-horizontal li {
      background: linear-gradient(135deg, #ffffff 0%, #f8faff 100%);
      border-radius: 16px;
      box-shadow:
        0 4px 16px rgba(37,99,235,0.06),
        0 2px 4px rgba(37,99,235,0.04),
        inset 0 1px 0 rgba(255,255,255,0.8);
      border: 1px solid rgba(37,99,235,0.08);
      padding: 1rem;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      position: relative;
      overflow: hidden;
      min-width: 280px;
      max-width: 320px;
      flex-shrink: 0;
      height: 80px;
    }

    .employee-list-horizontal li::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #2563eb, #3b82f6, #06b6d4);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    .employee-list-horizontal li:hover {
      transform: translateY(-4px) scale(1.02);
      box-shadow:
        0 16px 40px rgba(37,99,235,0.12),
        0 8px 16px rgba(37,99,235,0.08),
        inset 0 1px 0 rgba(255,255,255,0.9);
      border-color: rgba(37,99,235,0.2);
    }

    .employee-list-horizontal li:hover::before {
      opacity: 1;
    }

    .employee-list-horizontal li.active {
      background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
      color: #fff;
      transform: translateY(-2px) scale(1.01);
      box-shadow:
        0 12px 32px rgba(37,99,235,0.25),
        0 4px 12px rgba(37,99,235,0.15);
      border-color: #1d4ed8;
    }

    .employee-list-horizontal li.active::before {
      opacity: 1;
      background: linear-gradient(90deg, #60a5fa, #93c5fd, #dbeafe);
    }

    .employee-list-horizontal li.active .emp-name {
      color: #fff;
      font-weight: 900;
    }

    .employee-list-horizontal li.active .find-btn {
      background: rgba(255,255,255,0.15);
      color: #fff;
      border-color: rgba(255,255,255,0.2);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }

    .employee-list-horizontal .avatar-img {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      object-fit: cover;
      border: 2.5px solid rgba(37,99,235,0.1);
      background: #fff;
      box-shadow:
        0 4px 12px rgba(0,0,0,0.08),
        0 2px 4px rgba(0,0,0,0.04);
      flex-shrink: 0;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
    }

    .employee-list-horizontal li:hover .avatar-img {
      border-color: rgba(37,99,235,0.3);
      transform: scale(1.08);
      box-shadow:
        0 12px 24px rgba(0,0,0,0.12),
        0 4px 8px rgba(0,0,0,0.06);
    }

    .employee-list-horizontal li.active .avatar-img {
      border-color: rgba(255,255,255,0.4);
      transform: scale(1.05);
      box-shadow:
        0 8px 20px rgba(0,0,0,0.2),
        0 2px 6px rgba(0,0,0,0.1);
    }
    .employee-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 0.125rem;
      min-width: 0;
      overflow: hidden;
    }

    .employee-list-horizontal .emp-name {
      font-weight: 700;
      font-size: 0.95rem;
      color: #1f2937;
      letter-spacing: -0.01em;
      line-height: 1.2;
      margin: 0;
      transition: color 0.3s ease;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .employee-status {
      font-size: 0.75rem;
      color: #6b7280;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.375rem;
      white-space: nowrap;
    }

    .status-indicator {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background: #10b981;
      box-shadow: 0 0 0 1.5px rgba(16, 185, 129, 0.2);
      flex-shrink: 0;
    }
    .employee-list-horizontal .find-btn {
      background: linear-gradient(135deg, #2563eb 0%, #3b82f6 100%);
      color: #fff;
      border: none;
      border-radius: 10px;
      padding: 0.5rem 0.875rem;
      font-size: 0.8rem;
      font-weight: 600;
      box-shadow:
        0 3px 8px rgba(37,99,235,0.25),
        0 1px 3px rgba(37,99,235,0.1);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      display: flex;
      align-items: center;
      gap: 0.375rem;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      flex-shrink: 0;
      white-space: nowrap;
    }

    .employee-list-horizontal .find-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s ease;
    }

    .employee-list-horizontal .find-btn:hover::before {
      left: 100%;
    }

    .employee-list-horizontal .find-btn .fa-search {
      color: #fff;
      font-size: 0.9em;
      transition: transform 0.3s ease;
    }

    .employee-list-horizontal .find-btn:hover,
    .employee-list-horizontal .find-btn:focus {
      transform: translateY(-2px) scale(1.05);
      box-shadow:
        0 8px 20px rgba(37,99,235,0.3),
        0 4px 8px rgba(37,99,235,0.15);
    }

    .employee-list-horizontal .find-btn:hover .fa-search {
      transform: scale(1.1);
    }

    .employee-list-horizontal .find-btn:active {
      transform: translateY(0) scale(1.02);
    }

    /* Focus styly pro accessibility */
    .employee-list-horizontal li:focus {
      outline: 3px solid #2563eb;
      outline-offset: 2px;
      box-shadow: 0 6px 24px rgba(37,99,235,0.2);
    }

    .search-box-horizontal:focus-visible {
      outline: 3px solid #2563eb;
      outline-offset: 2px;
    }

    .employee-list-horizontal .find-btn:focus-visible {
      outline: 2px solid #2563eb;
      outline-offset: 2px;
    }
    .search-container {
      position: relative;
      flex: 1;
      max-width: 350px;
    }

    .search-box-horizontal {
      width: 100%;
      border-radius: 12px;
      border: 1.5px solid rgba(37,99,235,0.1);
      padding: 0.75rem 3rem 0.75rem 1rem;
      font-size: 0.9rem;
      background: rgba(255,255,255,0.8);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      outline: none;
      color: #1f2937;
      font-weight: 500;
      box-shadow:
        0 2px 8px rgba(37,99,235,0.06),
        inset 0 1px 0 rgba(255,255,255,0.8);
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
      height: 44px;
    }

    .search-box-horizontal:focus {
      border-color: #2563eb;
      background: rgba(255,255,255,0.95);
      box-shadow:
        0 8px 24px rgba(37,99,235,0.15),
        0 0 0 4px rgba(37,99,235,0.1),
        inset 0 1px 0 rgba(255,255,255,0.9);
      transform: translateY(-2px);
    }

    .search-box-horizontal::placeholder {
      color: #9ca3af;
      font-weight: 400;
    }

    .search-clear-btn {
      position: absolute;
      right: 1rem;
      top: 50%;
      transform: translateY(-50%);
      background: rgba(107,114,128,0.1);
      border: none;
      color: #6b7280;
      cursor: pointer;
      padding: 0.5rem;
      border-radius: 8px;
      transition: all 0.3s ease;
      opacity: 0;
      pointer-events: none;
      width: 2rem;
      height: 2rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .search-clear-btn.visible {
      opacity: 1;
      pointer-events: auto;
    }

    .search-clear-btn:hover {
      color: #dc2626;
      background: rgba(220,38,38,0.1);
      transform: translateY(-50%) scale(1.1);
    }

    /* Tooltip pro employee items */
    .employee-tooltip {
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: #1f2937;
      color: #fff;
      padding: 8px 12px;
      border-radius: 8px;
      font-size: 0.9rem;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s ease;
      z-index: 1000;
      margin-bottom: 8px;
    }

    .employee-tooltip::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 5px solid transparent;
      border-top-color: #1f2937;
    }

    .employee-list-horizontal li:hover .employee-tooltip {
      opacity: 1;
    }

    /* Responsive design pro employee bar */
    @media (max-width: 1024px) {
      .employee-bar {
        margin: 0 1rem 1rem 1rem;
        padding: 1rem 1.25rem;
      }
      .employee-list-horizontal li {
        min-width: 260px;
        max-width: 300px;
      }
    }

    @media (max-width: 768px) {
      .employee-bar {
        padding: 0.875rem 1rem;
        margin: 0 0.5rem 1rem 0.5rem;
        border-radius: 16px;
        min-height: 100px;
      }
      .search-header {
        padding: 0.625rem 0.75rem;
        margin-bottom: 0.75rem;
        height: 50px;
      }
      .search-section {
        gap: 0.75rem;
      }
      .search-container {
        max-width: 280px;
      }
      .search-box-horizontal {
        font-size: 0.85rem;
        padding: 0.625rem 2.5rem 0.625rem 0.875rem;
        height: 38px;
      }
      .search-counter {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }
      .employee-list-horizontal li {
        min-width: 240px;
        max-width: 280px;
        height: 70px;
        padding: 0.75rem;
        gap: 0.625rem;
      }
      .employee-list-horizontal .avatar-img {
        width: 40px;
        height: 40px;
      }
      .employee-list-horizontal .emp-name {
        font-size: 0.875rem;
      }
      .employee-status {
        font-size: 0.7rem;
      }
      .employee-list-horizontal .find-btn {
        padding: 0.375rem 0.625rem;
        font-size: 0.75rem;
      }
    }

    @media (max-width: 480px) {
      .employee-bar {
        padding: 0.75rem;
        margin: 0 0.25rem 0.75rem 0.25rem;
        border-radius: 14px;
        min-height: 90px;
      }
      .search-header {
        padding: 0.5rem;
        height: 45px;
        margin-bottom: 0.5rem;
      }
      .search-section {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
      }
      .search-container {
        max-width: 100%;
      }
      .search-box-horizontal {
        padding: 0.5rem 2.25rem 0.5rem 0.75rem;
        font-size: 0.8rem;
        height: 36px;
      }
      .search-counter {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
        text-align: center;
      }
      .employee-list-horizontal li {
        min-width: 200px;
        max-width: 240px;
        height: 60px;
        padding: 0.625rem;
        gap: 0.5rem;
      }
      .employee-list-horizontal .avatar-img {
        width: 36px;
        height: 36px;
      }
      .employee-list-horizontal .emp-name {
        font-size: 0.8rem;
      }
      .employee-status {
        font-size: 0.65rem;
      }
      .employee-list-horizontal .find-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
        gap: 0.25rem;
      }
    }

    /* Hlavní layout styly */
    .main-flex-custom {
      flex-direction: column;
      gap: 32px;
      align-items: stretch;
    }

    .mapa-wrapper-custom {
      margin-left: 0;
      justify-content: center;
    }

    .search-header {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .search-counter {
      color: #6b7280;
      font-size: 0.8rem;
      font-weight: 500;
      white-space: nowrap;
      background: rgba(255,255,255,0.6);
      padding: 0.375rem 0.75rem;
      border-radius: 8px;
      border: 1px solid rgba(37,99,235,0.08);
    }
  </style>
</head>
<body>
  <div class="main-flex main-flex-custom">
    <div class="employee-bar">
      <div class="search-header">
        <div class="search-section">
          <div class="search-container">
            <input class="search-box-horizontal" type="text" id="searchInput" placeholder="Vyhledat zaměstnance...">
            <button type="button" class="search-clear-btn" id="searchClearBtn" title="Vymazat vyhledávání">
              <i class="fas fa-times"></i>
            </button>
          </div>
          <div id="searchCounter" class="search-counter"></div>
        </div>
      </div>
      <ul class="employee-list-horizontal" id="employeeList"></ul>
    </div>
    <div class="mapa-wrapper mapa-wrapper-custom">
      <div class="office-map-container">
        <img id="office-map-img" src="img/Greenline.png" alt="Plán kanceláře">
      </div>
    </div>
  </div>
  <div id="img-error" class="error-message">Obrázek plánu kanceláře se nepodařilo načíst. Zkontrolujte cestu k souboru <b>img/Greenline.png</b> nebo kontaktujte správce.</div>
  <script>
    // --- MARKERY V PIXELECH ---
    let markerPositions = [
      {
        "jmeno": "Smrček Petr",
        "left": 954,
        "top": 610
      },
      {
        "jmeno": "Mičáň Alex",
        "left": 891,
        "top": 579
      },
      {
        "jmeno": "Vích Ondřej",
        "left": 1029,
        "top": 614
      },
      {
        "jmeno": "Tůma Tomáš",
        "left": 1140,
        "top": 616
      },
      {
        "jmeno": "Hrdá Veronika",
        "left": 1691,
        "top": 170
      },
      {
        "jmeno": "Vlčková Soňa",
        "left": 1689,
        "top": 211
      },
      {
        "jmeno": "Hons Jindřich",
        "left": 1607,
        "top": 170
      },
      {
        "jmeno": "Beáta Barošová",
        "left": 1563,
        "top": 169
      }
    ];

    // Pomocné funkce pro odstranění diakritiky a porovnání jmen
    function normalizeName(str) {
      return str
        .normalize('NFD')
        .replace(/\p{Diacritic}/gu, '')
        .toLowerCase()
        .replace(/\s+/g, ' ')
        .trim();
    }

    // Vytvořím pole všech avatarů z index.html (ručně vygenerované nebo importované)
    const avatarList = [
      { src: 'img/Barošová.png', alt: 'Beáta Barošová' },
      { src: 'img/Bednář.png', alt: 'Bednář Petr' },
      { src: 'img/Bočánek.jpeg', alt: 'Bočánek Stanislav' },
      { src: 'img/Boháč.jpeg', alt: 'Kryštof Boháč' },
      { src: 'img/Bok.png', alt: 'Bok Zbyněk' },
      { src: 'img/Brzobohata.png', alt: 'Jana Brzobohatá' },
      { src: 'img/Česáková.jpeg', alt: 'Česáková Andrea' },
      { src: 'img/Čermák.png', alt: 'Čermák Martin' },
      { src: 'img/Drdák.jfif', alt: 'Drdák Josef' },
      { src: 'img/Dvořák.jpeg', alt: 'Dvořák Tomáš' },
      { src: 'img/Dvořáková.jpeg', alt: 'Váchalová Zuzana' },
      { src: 'img/Erhartová.jpeg', alt: 'Erhartová Pavla' },
      { src: 'img/Fridrichová.png', alt: 'Fridrichová Katarína' },
      { src: 'img/Gabriel.png', alt: 'Gabriel Martina' },
      { src: 'img/Gregor.png', alt: 'Gregor Boris' },
      { src: 'img/Haufenhoferova.png', alt: 'Haufenhoferová Eva' },
      { src: 'img/Hesko.png', alt: 'Martin Hesko' },
      { src: 'img/Hodánek.png', alt: 'Hodánek Jaroslav' },
      { src: 'img/Hons.png', alt: 'Hons Jindřich' },
      { src: 'img/Houdek.png', alt: 'Houdek Ondřej' },
      { src: 'img/Hrdá.jpeg', alt: 'Hrdá Veronika' },
      { src: 'img/Hůlová.jpg', alt: 'Hůlová Helena' },
      { src: 'img/Chemišinec.jpeg', alt: 'Chemišinec Igor' },
      { src: 'img/Jedličková1.png', alt: 'Markéta Jedličková' },
      { src: 'img/Jahoda.png', alt: 'Jahoda Vojtěch' },
      { src: 'img/Jurásková.png', alt: 'Pavlína Jurásková' },
      { src: 'img/Kalábová.jpeg', alt: 'Kalábová Lucie' },
      { src: 'img/Karas.jpg', alt: 'Karas Karel' },
      { src: 'img/Kníže.jpeg', alt: 'Kníže Jaromír' },
      { src: 'img/Knop.png', alt: 'Knop Ondřej' },
      { src: 'img/Kohoutová.jpeg', alt: 'Kohoutová Kateřina' },
      { src: 'img/Kopecká.png', alt: 'Kopecká Zuzana' },
      { src: 'img/Kreuzman.png', alt: 'Kreuzman Jiří' },
      { src: 'img/Křivánek.jpg', alt: 'Křivánek Libor' },
      { src: 'img/Kurfiřtová.jpeg', alt: 'Kurfiřtová Pavla' },
      { src: 'img/Laco.jpeg', alt: 'Laco Dušan' },
      { src: 'img/Lebeda.jpg', alt: 'Lebeda Dušan' },
      { src: 'img/Lobotková.png', alt: 'Lobotková Alena' },
      { src: 'img/Máca.png', alt: 'Máca Ondřej' },
      { src: 'img/Mácová.jpeg', alt: 'Mácová Michaela' },
      { src: 'img/Mašková.png', alt: 'Mašková Hana' },
      { src: 'img/Marešová.jpeg', alt: 'Nardelli Magdalena' },
      { src: 'img/Mlynarčíková.jpeg', alt: 'Michaela Mlynarčíková' },
      { src: 'img/Mňuková.png', alt: 'Mňuková Kateřina' },
      { src: 'img/Mesteková.jpg', alt: 'Alice Mesteková' },
      { src: 'img/Mican.png', alt: 'Mičáň Alex' },
      { src: 'img/Nečesaný.jpeg', alt: 'Nečesaný Jakub' },
      { src: 'img/Novák.png', alt: 'Novák Petr' },
      { src: 'img/Pešková.jpeg', alt: 'Pešková Monika' },
      { src: 'img/Prihara.png', alt: 'Prihara Roman' },
      { src: 'img/Procházková.jpeg', alt: 'Procházková Kateřina' },
      { src: 'img/Puchel.jpg', alt: 'Puchel Michal' },
      { src: 'img/Ráška.png', alt: 'Raška Michal' },
      { src: 'img/Akaki.jpeg', alt: 'Sharashenidze Akaki' },
      { src: 'img/Smrček.png', alt: 'Smrček Petr' },
      { src: 'img/Sojka.png', alt: 'Sojka Alena' },
      { src: 'img/Soukupovápng.png', alt: 'Soukupová Michaela' },
      { src: 'img/Srb.jpg', alt: 'Srb Václav' },
      { src: 'img/Staňková.png', alt: 'Staňková Michaela' },
      { src: 'img/Stašková.png', alt: 'Stašková Zuzana' },
      { src: 'img/Ševčenko.jpeg', alt: 'Ševčenko Peter' },
      { src: 'img/Šrom.png', alt: 'Šrom Jakub' },
      { src: 'img/Špala.jpeg', alt: 'Špala Jaroslav' },
      { src: 'img/Tepličanec.png', alt: 'Tepličanec Pavel' },
      { src: 'img/Tomek.png', alt: 'Tomek Jiří' },
      { src: 'img/Tůma.png', alt: 'Tůma Tomáš' },
      { src: 'img/Vacek.png', alt: 'Vacek Jaroslav' },
      { src: 'img/Varvara.jpeg', alt: 'Vasjuňkina Varvara' },
      { src: 'img/Vích.jpeg', alt: 'Vích Ondřej' },
      { src: 'img/Vichrová.jpeg', alt: 'Vichrová Martina' },
      { src: 'img/Vlčková.jpeg', alt: 'Vlčková Soňa' },
      { src: 'img/Záviský.jpg', alt: 'Záviský Ondřej' }
    ];

    function getAvatarForName(jmeno) {
      const norm = normalizeName(jmeno);
      for (const av of avatarList) {
        const alt = normalizeName(av.alt);
        // Páruj podle shody obou slov (jméno i příjmení, v libovolném pořadí)
        const parts = norm.split(' ');
        if (parts.every(p => alt.includes(p))) {
          return av.src;
        }
      }
      return 'img/no-person-photo.png';
    }

    // Collapsible panel
    let panelCollapsed = false;
    function togglePanel() {
      const panel = document.querySelector('.side-panel');
      panelCollapsed = !panelCollapsed;
      panel.classList.toggle('collapsed', panelCollapsed);
      document.getElementById('collapse-btn').innerHTML = panelCollapsed ? '⮞' : '⮜';
    }

    // Fallback data pro případ, že se nepodaří načíst JSON soubor
    const fallbackZamestnanci = [
      { "jmeno": "Beáta Barošová" },
      { "jmeno": "Bednář Petr" },
      { "jmeno": "Bílek Milan" },
      { "jmeno": "Bočánek Stanislav" },
      { "jmeno": "Boháč Kryštof" },
      { "jmeno": "Bok Zbyněk" },
      { "jmeno": "Brzobohatá Jana" },
      { "jmeno": "Česáková Andrea" },
      { "jmeno": "Čermák Martin" },
      { "jmeno": "Drdák Josef" },
      { "jmeno": "Dvořák Tomáš" },
      { "jmeno": "Váchalová Zuzana" },
      { "jmeno": "Erhartová Pavla" },
      { "jmeno": "Fridrichová Katarína" },
      { "jmeno": "Gabriel Martina" },
      { "jmeno": "Gregor Boris" },
      { "jmeno": "Haufenhoferová Eva" },
      { "jmeno": "Hesko Martin" },
      { "jmeno": "Hodánek Jaroslav" },
      { "jmeno": "Hons Jindřich" },
      { "jmeno": "Horová Žaneta" },
      { "jmeno": "Houdek Ondřej" },
      { "jmeno": "Hrdá Veronika" },
      { "jmeno": "Hůlová Helena" },
      { "jmeno": "Chemišinec Igor" },
      { "jmeno": "Jedličková Markéta" },
      { "jmeno": "Jahoda Vojtěch" },
      { "jmeno": "Jindrová Jiřina" },
      { "jmeno": "Jindrová Petra" },
      { "jmeno": "Pavlína Jurásková" },
      { "jmeno": "Kalábová Lucie" },
      { "jmeno": "Kánský Jiří" },
      { "jmeno": "Karas Karel" },
      { "jmeno": "Kníže Jaromír" },
      { "jmeno": "Knop Ondřej" },
      { "jmeno": "Kohoutová Kateřina" },
      { "jmeno": "Kopecká Zuzana" },
      { "jmeno": "Kreuzman Jiří" },
      { "jmeno": "Křivánek Libor" },
      { "jmeno": "Kurfiřtová Pavla" },
      { "jmeno": "Laco Dušan" },
      { "jmeno": "Láník Libor" },
      { "jmeno": "Lebeda Dušan" },
      { "jmeno": "Lobotková Alena" },
      { "jmeno": "Máca Ondřej" },
      { "jmeno": "Mácová Michaela" },
      { "jmeno": "Mareš Jan" },
      { "jmeno": "Mašková Hana" },
      { "jmeno": "Nardelli Magdalena" },
      { "jmeno": "Michaela Mlynarčíková" },
      { "jmeno": "Mňuková Kateřina" },
      { "jmeno": "Mesteková Alice" },
      { "jmeno": "Mičáň Alex" },
      { "jmeno": "Nečesaný Jakub" },
      { "jmeno": "Nohejlová Jiřina" },
      { "jmeno": "Novák Petr" },
      { "jmeno": "Pecánek Tomáš" },
      { "jmeno": "Pešková Monika" },
      { "jmeno": "Prihara Roman" },
      { "jmeno": "Procházková Kateřina" },
      { "jmeno": "Puchel Michal" },
      { "jmeno": "Raška Michal" },
      { "jmeno": "Rýdl Zdeněk" },
      { "jmeno": "Ryšavý Miroslav" },
      { "jmeno": "Sharashenidze Akaki" },
      { "jmeno": "Smrček Petr" },
      { "jmeno": "Sojka Alena" },
      { "jmeno": "Soukupová Michaela" },
      { "jmeno": "Srb Václav" },
      { "jmeno": "Staňková Michaela" },
      { "jmeno": "Stašková Zuzana" },
      { "jmeno": "Ševčenko Peter" },
      { "jmeno": "Šmídek Filip" },
      { "jmeno": "Šrom Jakub" },
      { "jmeno": "Špala Jaroslav" },
      { "jmeno": "Tepličanec Pavel" },
      { "jmeno": "Tomek Jiří" },
      { "jmeno": "Tůma Tomáš" },
      { "jmeno": "Vacek Jaroslav" },
      { "jmeno": "Valent Lajos" },
      { "jmeno": "Vasjuňkina Varvara" },
      { "jmeno": "Vích Ondřej" },
      { "jmeno": "Vichrová Martina" },
      { "jmeno": "Vlčková Soňa" },
      { "jmeno": "Záviský Ondřej" },
      { "jmeno": "Zelenková Jana" },
      { "jmeno": "Zezuláková Andrea" }
    ];

    // Načtení zaměstnanců
    let zamestnanci = [];

    // Zobrazit loading state
    function showLoadingState() {
      const ul = document.getElementById('employeeList');
      ul.innerHTML = `
        <li style="
          justify-content: center;
          color: #6b7280;
          font-style: italic;
          padding: 2rem;
          background: #f8faff;
          border-radius: 16px;
          border: 2px solid #e3e8f0;
          min-height: 70px;
          display: flex;
          align-items: center;
          gap: 10px;
        ">
          <i class="fas fa-spinner fa-spin" style="color: #2563eb;"></i>
          Načítání zaměstnanců...
        </li>
      `;
    }

    // Inicializace dat
    function initializeData() {
      showLoadingState();

      // Pokusit se načíst ze souboru, jinak použít fallback data
      fetch('zamestnanci.json')
        .then(res => {
          if (!res.ok) throw new Error('Network response was not ok');
          return res.json();
        })
        .then(data => {
          console.log('Data načtena ze souboru zamestnanci.json');
          zamestnanci = data;
          renderEmployeeList(zamestnanci);
          renderMarkers(zamestnanci);
          enableMappingMode();
        })
        .catch(error => {
          console.log('Nepodařilo se načíst soubor, používám fallback data:', error);
          // Použít fallback data
          zamestnanci = fallbackZamestnanci;
          renderEmployeeList(zamestnanci);
          renderMarkers(zamestnanci);
          enableMappingMode();
        });
    }

    // Spustit inicializaci
    initializeData();

    function renderEmployeeList(list) {
      const ul = document.getElementById('employeeList');
      const counter = document.getElementById('searchCounter');
      if (!ul) return;

      // Aktualizovat počítadlo
      if (counter) {
        if (list.length === zamestnanci.length) {
          counter.textContent = `${list.length} zaměstnanců`;
        } else {
          counter.textContent = `${list.length} z ${zamestnanci.length} zaměstnanců`;
        }
      }

      // Fade out animation
      ul.style.opacity = '0.5';
      ul.style.transform = 'translateY(10px)';

      setTimeout(() => {
        ul.innerHTML = '';
        list.forEach((z, index) => {
        const li = document.createElement('li');
        li.tabIndex = 0;
        li.dataset.jmeno = z.jmeno;
        // Avatar
        const avatar = document.createElement('img');
        avatar.src = getAvatarForName(z.jmeno);
        avatar.alt = z.jmeno;
        avatar.className = 'avatar-img';
        avatar.onerror = () => { avatar.src = 'img/no-person-photo.png'; };
        li.appendChild(avatar);

        // Employee info container
        const infoContainer = document.createElement('div');
        infoContainer.className = 'employee-info';

        // Jméno
        const nameElement = document.createElement('h3');
        nameElement.textContent = z.jmeno;
        nameElement.className = 'emp-name';
        nameElement.title = z.jmeno;
        infoContainer.appendChild(nameElement);

        // Status
        const statusElement = document.createElement('div');
        statusElement.className = 'employee-status';
        statusElement.innerHTML = '<div class="status-indicator"></div>Dostupný';
        infoContainer.appendChild(statusElement);

        li.appendChild(infoContainer);

        // Tlačítko najít
        const btn = document.createElement('button');
        btn.innerHTML = '<i class="fas fa-map-marker-alt"></i> Najít';
        btn.className = 'find-btn';
        btn.title = 'Zobrazit na mapě';
        btn.onclick = e => { e.stopPropagation(); highlightByName(z.jmeno, true); };
        li.appendChild(btn);
        // Celá položka klikací
        li.onclick = e => {
          e.preventDefault();
          highlightByName(z.jmeno, true);
        };

        // Keyboard navigation
        li.addEventListener('keydown', e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            highlightByName(z.jmeno, true);
          } else if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
            e.preventDefault();
            navigateEmployeeList(e.key === 'ArrowRight' ? 1 : -1, li);
          }
        });
          // Animace pro nové položky
          li.style.opacity = '0';
          li.style.transform = 'translateX(-20px)';
          ul.appendChild(li);

          // Staggered animation
          setTimeout(() => {
            li.style.transition = 'all 0.3s ease';
            li.style.opacity = '1';
            li.style.transform = 'translateX(0)';
          }, index * 50);
        });

        // Fade in animation pro celý seznam
        setTimeout(() => {
          ul.style.transition = 'all 0.3s ease';
          ul.style.opacity = '1';
          ul.style.transform = 'translateY(0)';
        }, 100);
      }, 150);
    }

    function renderMarkers(list) {
      const container = document.querySelector('.office-map-container');
      // Odstranit staré markery
      container.querySelectorAll('.marker').forEach(m => m.remove());
      // Vykreslit nové markery podle markerPositions
      markerPositions.forEach((pos, i) => {
        const idx = list.findIndex(z => z.jmeno === pos.jmeno);
        if (idx === -1) return;
        const marker = document.createElement('div');
        marker.className = 'marker';
        marker.style.top = pos.top + 'px';
        marker.style.left = pos.left + 'px';
        marker.dataset.jmeno = pos.jmeno;
        marker.tabIndex = 0;
        // MODRÉ KOLEČKO místo avataru
        const blueDot = document.createElement('div');
        blueDot.style.width = '26px';
        blueDot.style.height = '26px';
        blueDot.style.borderRadius = '50%';
        blueDot.style.background = '#007bff';
        blueDot.style.boxShadow = '0 2px 8px rgba(0,123,255,0.18)';
        blueDot.style.border = '2.5px solid #fff';
        blueDot.style.display = 'block';
        marker.appendChild(blueDot);
        // Tooltip (skrytý, zobrazí se při hoveru/aktivaci)
        const tooltip = document.createElement('span');
        tooltip.className = 'tooltip';
        tooltip.innerHTML = `${pos.jmeno}<br><span class='coords'>left: ${pos.left}, top: ${pos.top}</span>`;
        marker.appendChild(tooltip);
        marker.addEventListener('click', () => highlightByName(pos.jmeno, true));
        // Drag & drop
        marker.addEventListener('mousedown', function(e) {
          e.preventDefault();
          startDragMarker(marker, i, e);
        });
        container.appendChild(marker);
      });
    }

    // Keyboard navigation pro horizontální seznam
    function navigateEmployeeList(direction, currentElement) {
      const allItems = Array.from(document.querySelectorAll('.employee-list-horizontal li'));
      const currentIndex = allItems.indexOf(currentElement);
      let nextIndex = currentIndex + direction;

      // Wrap around
      if (nextIndex < 0) nextIndex = allItems.length - 1;
      if (nextIndex >= allItems.length) nextIndex = 0;

      if (allItems[nextIndex]) {
        allItems[nextIndex].focus();
        allItems[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
      }
    }

    // Vylepšené vyhledávání s debounce
    let searchTimeout;
    const searchInput = document.getElementById('searchInput');
    const searchClearBtn = document.getElementById('searchClearBtn');

    function updateClearButton() {
      if (searchInput.value.trim()) {
        searchClearBtn.classList.add('visible');
      } else {
        searchClearBtn.classList.remove('visible');
      }
    }

    searchInput.addEventListener('input', function() {
      const val = this.value.toLowerCase().trim();
      updateClearButton();

      // Debounce pro lepší výkon
      clearTimeout(searchTimeout);
      searchTimeout = setTimeout(() => {
        if (val === '') {
          // Zobrazit všechny zaměstnance
          renderEmployeeList(zamestnanci);
        } else {
          // Filtrovat podle jména (podporuje částečné shody)
          const filtered = zamestnanci.filter(z => {
            const name = normalizeName(z.jmeno);
            const searchTerm = normalizeName(val);
            return name.includes(searchTerm);
          });
          renderEmployeeList(filtered);

          // Pokud je pouze jeden výsledek, automaticky ho zvýrazni
          if (filtered.length === 1) {
            setTimeout(() => highlightByName(filtered[0].jmeno, true), 300);
          }
        }
      }, 200);
    });

    // Clear button functionality
    searchClearBtn.addEventListener('click', function() {
      searchInput.value = '';
      updateClearButton();
      renderEmployeeList(zamestnanci);
      searchInput.focus();
    });

    // Zvýraznění markeru a jména, scroll na marker
    function highlightByName(jmeno, scrollTo) {
      // Zvýraznit marker na mapě
      document.querySelectorAll('.marker').forEach(marker => {
        const isActive = marker.dataset.jmeno === jmeno;
        marker.classList.toggle('active', isActive);
        if (isActive && scrollTo) {
          marker.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
        }
      });

      // Zvýraznit položku v horizontálním seznamu
      document.querySelectorAll('.employee-list-horizontal li').forEach(li => {
        const isActive = li.dataset.jmeno === jmeno;
        li.classList.toggle('active', isActive);
        if (isActive && scrollTo) {
          // Smooth scroll k aktivní položce v horizontálním seznamu
          li.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' });
        }
      });
    }

    // --- MAPOVACÍ REŽIM ---
    let mapped = [...markerPositions];
    function enableMappingMode() {
      const img = document.getElementById('office-map-img');
      img.style.cursor = 'crosshair';
      img.addEventListener('click', onMapClick);
    }
    function onMapClick(e) {
      const img = e.target;
      const rect = img.getBoundingClientRect();
      const left = Math.round(e.clientX - rect.left);
      const top = Math.round(e.clientY - rect.top);
      showMappingDialog(left, top);
    }
    function showMappingDialog(left, top) {
      // Dialog s výběrem jména
      const dialog = document.createElement('div');
      dialog.className = 'mapping-dialog';
      dialog.innerHTML = `
        <label for="mapping-select">Vyberte zaměstnance:</label>
        <select id="mapping-select">
          <option value="">-- Vyberte --</option>
          ${zamestnanci.map(z => `<option value="${z.jmeno}">${z.jmeno}</option>`).join('')}
        </select>
        <div>Souřadnice: <b>left: ${left}, top: ${top}</b></div>
        <button id="mapping-confirm">Přidat</button>
        <button id="mapping-cancel" style="background:#eee;color:#333;">Zrušit</button>
      `;
      document.body.appendChild(dialog);
      dialog.querySelector('#mapping-cancel').onclick = () => dialog.remove();
      dialog.querySelector('#mapping-confirm').onclick = () => {
        const jmeno = dialog.querySelector('#mapping-select').value;
        if (!jmeno) return;
        mapped.push({ jmeno, left, top });
        console.log('Aktuální pole markerPositions:', JSON.stringify(mapped, null, 2));
        dialog.remove();
        // Přidat marker na mapu s animací
        markerPositions = mapped;
        renderMarkers(zamestnanci);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => {
            if (m.dataset.jmeno === jmeno) m.classList.add('added');
          });
        }, 50);
        setTimeout(() => {
          document.querySelectorAll('.marker').forEach(m => m.classList.remove('added'));
        }, 800);
      };
    }

    // Kontrola načtení obrázku
    const img = document.getElementById('office-map-img');
    img.onerror = function() {
      document.querySelector('.office-map-container').style.display = 'none';
      document.getElementById('img-error').style.display = 'block';
    };
    img.onload = function() {
      document.getElementById('img-error').style.display = 'none';
      document.querySelector('.office-map-container').style.display = 'inline-block';
      renderMarkers(zamestnanci);
    };

    // Drag & drop logika
    let dragMarker = null;
    let dragIdx = null;
    let dragOffsetX = 0;
    let dragOffsetY = 0;
    function startDragMarker(marker, idx, e) {
      dragMarker = marker;
      dragIdx = idx;
      const rect = marker.getBoundingClientRect();
      dragOffsetX = e.clientX - rect.left;
      dragOffsetY = e.clientY - rect.top;
      document.addEventListener('mousemove', onDragMarker);
      document.addEventListener('mouseup', stopDragMarker);
      marker.classList.add('active');
      showTooltip(marker, markerPositions[idx].left, markerPositions[idx].top, true);
    }
    function onDragMarker(e) {
      if (!dragMarker) return;
      const container = document.querySelector('.office-map-container');
      const crect = container.getBoundingClientRect();
      let left = Math.round(e.clientX - crect.left - dragOffsetX + 13); // +13 pro střed markeru
      let top = Math.round(e.clientY - crect.top - dragOffsetY + 13);
      // Omezit na hranice obrázku
      left = Math.max(0, Math.min(left, container.offsetWidth - 26));
      top = Math.max(0, Math.min(top, container.offsetHeight - 26));
      dragMarker.style.left = left + 'px';
      dragMarker.style.top = top + 'px';
      showTooltip(dragMarker, left, top, true);
    }
    function stopDragMarker(e) {
      if (!dragMarker) return;
      const left = parseInt(dragMarker.style.left);
      const top = parseInt(dragMarker.style.top);
      markerPositions[dragIdx].left = left;
      markerPositions[dragIdx].top = top;
      dragMarker.classList.remove('active');
      showTooltip(dragMarker, left, top, false);
      dragMarker = null;
      dragIdx = null;
      document.removeEventListener('mousemove', onDragMarker);
      document.removeEventListener('mouseup', stopDragMarker);
      // Vypiš nové pole do konzole
      console.log('Aktuální pole markerPositions:', JSON.stringify(markerPositions, null, 2));
    }
    function showTooltip(marker, left, top, dragging) {
      const tooltip = marker.querySelector('.tooltip');
      tooltip.innerHTML = `${marker.dataset.jmeno}<br><span class='coords'>left: ${left}, top: ${top}${dragging ? ' <b>(přesouváš)</b>' : ''}</span>`;
      tooltip.style.opacity = 1;
    }

    // Fallback pro chybějící obrázky
    document.addEventListener('DOMContentLoaded', () => {
      document.querySelectorAll('.avatar-img').forEach(img => {
        img.onerror = () => { img.src = 'img/no-person-photo.png'; };
      });
    });
  </script>
</body>
</html> 